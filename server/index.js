const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const crypto = require('crypto');
const axios = require('axios');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// In-memory storage for plot configurations (in production, use a database)
const plotConfigs = {
  // Example: "12345": { secretKey: "your-secret-key-here" }
};

// Encryption/Decryption utilities
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';

function encrypt(text) {
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decrypt(encryptedText) {
  try {
    const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    return null;
  }
}

// Mojang API functions
async function validateMinecraftPlayer(username) {
  try {
    const response = await axios.get(`https://api.mojang.com/users/profiles/minecraft/${username}`);
    return {
      valid: true,
      uuid: response.data.id,
      username: response.data.name
    };
  } catch (error) {
    return { valid: false };
  }
}

function getPlayerHeadUrl(uuid) {
  return `https://crafatar.com/avatars/${uuid}?size=64&overlay`;
}

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'McDiamondFire Voice Chat Server' });
});

// Validate player and plot
app.post('/api/validate', async (req, res) => {
  const { plotId, encryptedUsername } = req.body;

  // Decrypt username
  const username = decrypt(encryptedUsername);
  if (!username) {
    return res.status(400).json({ error: 'Invalid encrypted username' });
  }

  // Validate plot exists and has secret key
  if (!plotConfigs[plotId]) {
    return res.status(404).json({ error: 'Plot not found or not configured for voice chat' });
  }

  // Validate Minecraft player
  const playerValidation = await validateMinecraftPlayer(username);
  if (!playerValidation.valid) {
    return res.status(400).json({ error: 'Invalid Minecraft username' });
  }

  res.json({
    valid: true,
    player: {
      username: playerValidation.username,
      uuid: playerValidation.uuid,
      headUrl: getPlayerHeadUrl(playerValidation.uuid)
    },
    plotId
  });
});

// Encrypt username (for Google Apps Script integration)
app.post('/api/encrypt-username', (req, res) => {
  const { username, apiKey } = req.body;

  // Validate API key (you should set this in environment variables)
  if (apiKey !== process.env.API_SECRET_KEY) {
    return res.status(401).json({ error: 'Invalid API key' });
  }

  const encrypted = encrypt(username);
  res.json({ encryptedUsername: encrypted });
});

// Add plot configuration (admin endpoint)
app.post('/api/admin/add-plot', (req, res) => {
  const { plotId, secretKey, adminKey } = req.body;

  // Validate admin key
  if (adminKey !== process.env.ADMIN_SECRET_KEY) {
    return res.status(401).json({ error: 'Invalid admin key' });
  }

  plotConfigs[plotId] = { secretKey };
  res.json({ message: 'Plot added successfully', plotId });
});

// Socket.IO for WebRTC signaling
const rooms = new Map(); // plotId -> Set of socket IDs

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-plot', (data) => {
    const { plotId, player } = data;

    // Leave any existing rooms
    socket.rooms.forEach(room => {
      if (room !== socket.id) {
        socket.leave(room);
        if (rooms.has(room)) {
          rooms.get(room).delete(socket.id);
        }
      }
    });

    // Join the plot room
    socket.join(plotId);
    socket.plotId = plotId;
    socket.player = player;

    // Track room membership
    if (!rooms.has(plotId)) {
      rooms.set(plotId, new Set());
    }
    rooms.get(plotId).add(socket.id);

    // Notify others in the room
    socket.to(plotId).emit('user-joined', {
      socketId: socket.id,
      player: player
    });

    // Send current room members to the new user
    const roomMembers = [];
    if (rooms.has(plotId)) {
      rooms.get(plotId).forEach(socketId => {
        if (socketId !== socket.id) {
          const memberSocket = io.sockets.sockets.get(socketId);
          if (memberSocket && memberSocket.player) {
            roomMembers.push({
              socketId: socketId,
              player: memberSocket.player
            });
          }
        }
      });
    }

    socket.emit('room-members', roomMembers);
  });

  // WebRTC signaling
  socket.on('offer', (data) => {
    socket.to(data.target).emit('offer', {
      offer: data.offer,
      sender: socket.id
    });
  });

  socket.on('answer', (data) => {
    socket.to(data.target).emit('answer', {
      answer: data.answer,
      sender: socket.id
    });
  });

  socket.on('ice-candidate', (data) => {
    socket.to(data.target).emit('ice-candidate', {
      candidate: data.candidate,
      sender: socket.id
    });
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);

    // Clean up room membership
    if (socket.plotId && rooms.has(socket.plotId)) {
      rooms.get(socket.plotId).delete(socket.id);

      // Notify others in the room
      socket.to(socket.plotId).emit('user-left', {
        socketId: socket.id
      });
    }
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`McDiamondFire Voice Chat Server running on port ${PORT}`);
});
