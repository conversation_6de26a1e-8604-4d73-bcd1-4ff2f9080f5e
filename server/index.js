const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const crypto = require('crypto');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Load plot configurations from JSON file
let plotConfigs = {};
try {
  const plotsData = fs.readFileSync(path.join(__dirname, '../plots.json'), 'utf8');
  plotConfigs = JSON.parse(plotsData);
} catch (error) {
  console.error('Error loading plots.json:', error);
  plotConfigs = {};
}

// Encryption/Decryption utilities (each plot has its own key)
function encrypt(text, encryptionKey) {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(encryptionKey, 'salt', 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return iv.toString('hex') + ':' + encrypted;
}

function decrypt(encryptedText, encryptionKey) {
  try {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(encryptionKey, 'salt', 32);

    const textParts = encryptedText.split(':');
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedData = textParts.join(':');

    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    return null;
  }
}

// Mojang API functions
async function getPlayerByUsername(username) {
  try {
    const response = await axios.get(`https://api.mojang.com/users/profiles/minecraft/${username}`);
    return {
      valid: true,
      uuid: response.data.id,
      username: response.data.name
    };
  } catch (error) {
    return { valid: false };
  }
}

async function getPlayerByUuid(uuid) {
  try {
    // Remove dashes from UUID if present
    const cleanUuid = uuid.replace(/-/g, '');
    const response = await axios.get(`https://sessionserver.mojang.com/session/minecraft/profile/${cleanUuid}`);
    return {
      valid: true,
      uuid: response.data.id,
      username: response.data.name
    };
  } catch (error) {
    return { valid: false };
  }
}

function getPlayerHeadUrl(uuid) {
  return `https://crafatar.com/avatars/${uuid}?size=64&overlay`;
}

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'McDiamondFire Voice Chat Server' });
});

// Validate player and plot
app.post('/api/validate', async (req, res) => {
  const { plotId, encryptedUuid } = req.body;

  // Validate plot exists
  if (!plotConfigs[plotId]) {
    return res.status(404).json({ error: 'Plot not found or not configured for voice chat' });
  }

  // Decrypt UUID using plot's encryption key
  const uuid = decrypt(encryptedUuid, plotConfigs[plotId].encryptionKey);
  if (!uuid) {
    return res.status(400).json({ error: 'Invalid encrypted UUID' });
  }

  // Validate Minecraft player by UUID
  const playerValidation = await getPlayerByUuid(uuid);
  if (!playerValidation.valid) {
    return res.status(400).json({ error: 'Invalid Minecraft player UUID' });
  }

  res.json({
    valid: true,
    player: {
      username: playerValidation.username,
      uuid: playerValidation.uuid,
      headUrl: getPlayerHeadUrl(playerValidation.uuid)
    },
    plotId
  });
});

// Generate voice chat URL (for McDiamondFire integration)
app.post('/api/generate-voice-url', async (req, res) => {
  const { username, plotId } = req.body;

  // Validate required fields
  if (!username || !plotId) {
    return res.status(400).json({ error: 'Missing required fields: username, plotId' });
  }

  // Validate plot exists
  if (!plotConfigs[plotId]) {
    return res.status(404).json({ error: 'Plot not found or not configured for voice chat' });
  }

  // Get player UUID from username
  const playerData = await getPlayerByUsername(username);
  if (!playerData.valid) {
    return res.status(400).json({ error: 'Invalid Minecraft username' });
  }

  // Encrypt UUID using plot's encryption key
  const encryptedUuid = encrypt(playerData.uuid, plotConfigs[plotId].encryptionKey);

  // Generate voice chat URL
  const baseUrl = process.env.BASE_URL || `http://localhost:5000`;
  const voiceChatUrl = `${baseUrl}?plot=${plotId}&user=${encodeURIComponent(encryptedUuid)}`;

  res.json({
    success: true,
    voiceChatUrl: voiceChatUrl,
    encryptedUuid: encryptedUuid,
    player: {
      username: playerData.username,
      uuid: playerData.uuid
    }
  });
});

// Add plot configuration (admin endpoint)
app.post('/api/admin/add-plot', (req, res) => {
  const { plotId, encryptionKey, name, owner, adminKey } = req.body;

  // Validate admin key
  if (adminKey !== process.env.ADMIN_SECRET_KEY) {
    return res.status(401).json({ error: 'Invalid admin key' });
  }

  // Add plot to config
  plotConfigs[plotId] = {
    encryptionKey: encryptionKey || `plot-${plotId}-${Date.now()}`,
    name: name || `Plot ${plotId}`,
    owner: owner || 'Unknown'
  };

  // Save to file
  try {
    fs.writeFileSync(path.join(__dirname, '../plots.json'), JSON.stringify(plotConfigs, null, 2));
    res.json({ message: 'Plot added successfully', plotId, config: plotConfigs[plotId] });
  } catch (error) {
    res.status(500).json({ error: 'Failed to save plot configuration' });
  }
});

// Socket.IO for WebRTC signaling
const rooms = new Map(); // plotId -> Set of socket IDs

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-plot', (data) => {
    const { plotId, player } = data;

    // Leave any existing rooms
    socket.rooms.forEach(room => {
      if (room !== socket.id) {
        socket.leave(room);
        if (rooms.has(room)) {
          rooms.get(room).delete(socket.id);
        }
      }
    });

    // Join the plot room
    socket.join(plotId);
    socket.plotId = plotId;
    socket.player = player;

    // Track room membership
    if (!rooms.has(plotId)) {
      rooms.set(plotId, new Set());
    }
    rooms.get(plotId).add(socket.id);

    // Notify others in the room
    socket.to(plotId).emit('user-joined', {
      socketId: socket.id,
      player: player
    });

    // Send current room members to the new user
    const roomMembers = [];
    if (rooms.has(plotId)) {
      rooms.get(plotId).forEach(socketId => {
        if (socketId !== socket.id) {
          const memberSocket = io.sockets.sockets.get(socketId);
          if (memberSocket && memberSocket.player) {
            roomMembers.push({
              socketId: socketId,
              player: memberSocket.player
            });
          }
        }
      });
    }

    socket.emit('room-members', roomMembers);
  });

  // WebRTC signaling
  socket.on('offer', (data) => {
    socket.to(data.target).emit('offer', {
      offer: data.offer,
      sender: socket.id
    });
  });

  socket.on('answer', (data) => {
    socket.to(data.target).emit('answer', {
      answer: data.answer,
      sender: socket.id
    });
  });

  socket.on('ice-candidate', (data) => {
    socket.to(data.target).emit('ice-candidate', {
      candidate: data.candidate,
      sender: socket.id
    });
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);

    // Clean up room membership
    if (socket.plotId && rooms.has(socket.plotId)) {
      rooms.get(socket.plotId).delete(socket.id);

      // Notify others in the room
      socket.to(socket.plotId).emit('user-left', {
        socketId: socket.id
      });
    }
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`McDiamondFire Voice Chat Server running on port ${PORT}`);
});
