const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const crypto = require('crypto');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Load plot configurations from JSON file
let plotConfigs = {};
try {
  const plotsData = fs.readFileSync(path.join(__dirname, '../plots.json'), 'utf8');
  plotConfigs = JSON.parse(plotsData);
} catch (error) {
  console.error('Error loading plots.json:', error);
  plotConfigs = {};
}

// Encryption/Decryption utilities (each plot has its own key)
function encrypt(text, encryptionKey) {
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decrypt(encryptedText, encryptionKey) {
  try {
    const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    return null;
  }
}

// Mojang API functions
async function validateMinecraftPlayer(username) {
  try {
    const response = await axios.get(`https://api.mojang.com/users/profiles/minecraft/${username}`);
    return {
      valid: true,
      uuid: response.data.id,
      username: response.data.name
    };
  } catch (error) {
    return { valid: false };
  }
}

function getPlayerHeadUrl(uuid) {
  return `https://crafatar.com/avatars/${uuid}?size=64&overlay`;
}

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'McDiamondFire Voice Chat Server' });
});

// Validate player and plot
app.post('/api/validate', async (req, res) => {
  const { plotId, encryptedUsername } = req.body;

  // Validate plot exists
  if (!plotConfigs[plotId]) {
    return res.status(404).json({ error: 'Plot not found or not configured for voice chat' });
  }

  // Decrypt username using plot's encryption key
  const username = decrypt(encryptedUsername, plotConfigs[plotId].encryptionKey);
  if (!username) {
    return res.status(400).json({ error: 'Invalid encrypted username' });
  }

  // Validate Minecraft player
  const playerValidation = await validateMinecraftPlayer(username);
  if (!playerValidation.valid) {
    return res.status(400).json({ error: 'Invalid Minecraft username' });
  }

  res.json({
    valid: true,
    player: {
      username: playerValidation.username,
      uuid: playerValidation.uuid,
      headUrl: getPlayerHeadUrl(playerValidation.uuid)
    },
    plotId
  });
});

// Generate voice chat URL (for McDiamondFire integration)
app.post('/api/generate-voice-url', (req, res) => {
  const { username, plotId, secretKey } = req.body;

  // Validate required fields
  if (!username || !plotId || !secretKey) {
    return res.status(400).json({ error: 'Missing required fields: username, plotId, secretKey' });
  }

  // Validate plot exists and secret key matches
  if (!plotConfigs[plotId] || plotConfigs[plotId].secretKey !== secretKey) {
    return res.status(401).json({ error: 'Invalid plot ID or secret key' });
  }

  // Encrypt username
  const encryptedUsername = encrypt(username);

  // Generate voice chat URL
  const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 5000}`;
  const voiceChatUrl = `${baseUrl}?plot=${plotId}&user=${encodeURIComponent(encryptedUsername)}`;

  res.json({
    success: true,
    voiceChatUrl: voiceChatUrl,
    encryptedUsername: encryptedUsername
  });
});

// Add plot configuration (admin endpoint)
app.post('/api/admin/add-plot', (req, res) => {
  const { plotId, secretKey, adminKey } = req.body;

  // Validate admin key
  if (adminKey !== process.env.ADMIN_SECRET_KEY) {
    return res.status(401).json({ error: 'Invalid admin key' });
  }

  plotConfigs[plotId] = { secretKey };
  res.json({ message: 'Plot added successfully', plotId });
});

// Socket.IO for WebRTC signaling
const rooms = new Map(); // plotId -> Set of socket IDs

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-plot', (data) => {
    const { plotId, player } = data;

    // Leave any existing rooms
    socket.rooms.forEach(room => {
      if (room !== socket.id) {
        socket.leave(room);
        if (rooms.has(room)) {
          rooms.get(room).delete(socket.id);
        }
      }
    });

    // Join the plot room
    socket.join(plotId);
    socket.plotId = plotId;
    socket.player = player;

    // Track room membership
    if (!rooms.has(plotId)) {
      rooms.set(plotId, new Set());
    }
    rooms.get(plotId).add(socket.id);

    // Notify others in the room
    socket.to(plotId).emit('user-joined', {
      socketId: socket.id,
      player: player
    });

    // Send current room members to the new user
    const roomMembers = [];
    if (rooms.has(plotId)) {
      rooms.get(plotId).forEach(socketId => {
        if (socketId !== socket.id) {
          const memberSocket = io.sockets.sockets.get(socketId);
          if (memberSocket && memberSocket.player) {
            roomMembers.push({
              socketId: socketId,
              player: memberSocket.player
            });
          }
        }
      });
    }

    socket.emit('room-members', roomMembers);
  });

  // WebRTC signaling
  socket.on('offer', (data) => {
    socket.to(data.target).emit('offer', {
      offer: data.offer,
      sender: socket.id
    });
  });

  socket.on('answer', (data) => {
    socket.to(data.target).emit('answer', {
      answer: data.answer,
      sender: socket.id
    });
  });

  socket.on('ice-candidate', (data) => {
    socket.to(data.target).emit('ice-candidate', {
      candidate: data.candidate,
      sender: socket.id
    });
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);

    // Clean up room membership
    if (socket.plotId && rooms.has(socket.plotId)) {
      rooms.get(socket.plotId).delete(socket.id);

      // Notify others in the room
      socket.to(socket.plotId).emit('user-left', {
        socketId: socket.id
      });
    }
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`McDiamondFire Voice Chat Server running on port ${PORT}`);
});
