class VoiceChat {
    constructor() {
        this.socket = null;
        this.localStream = null;
        this.peerConnections = new Map();
        this.currentPlayer = null;
        this.plotId = null;
        this.isConnected = false;
        this.isMicEnabled = false;
        
        this.init();
    }

    async init() {
        // Parse URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        this.plotId = urlParams.get('plot');
        const encryptedUsername = urlParams.get('user');

        if (!this.plotId || !encryptedUsername) {
            this.showError('Missing plot ID or username in URL. Please use the correct link from McDiamondFire.');
            return;
        }

        // Validate credentials with server
        try {
            const response = await fetch('/api/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plotId: this.plotId,
                    encryptedUsername: encryptedUsername
                })
            });

            const data = await response.json();

            if (!response.ok) {
                this.showError(data.error || 'Failed to validate credentials');
                return;
            }

            this.currentPlayer = data.player;
            this.setupVoiceChat();

        } catch (error) {
            this.showError('Failed to connect to server. Please try again later.');
            console.error('Validation error:', error);
        }
    }

    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('error-message').textContent = message;
    }

    setupVoiceChat() {
        // Hide loading, show voice chat interface
        document.getElementById('loading').style.display = 'none';
        document.getElementById('voice-chat').style.display = 'block';

        // Set player info
        document.getElementById('plot-id').textContent = this.plotId;
        document.getElementById('username').textContent = this.currentPlayer.username;

        // Add current player to grid
        this.addPlayerToGrid(this.currentPlayer, true);

        // Setup Socket.IO connection
        this.setupSocket();

        // Setup event listeners
        this.setupEventListeners();
    }

    setupSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.isConnected = true;
            this.updateStatus('Connected to voice chat server');
            
            // Join the plot room
            this.socket.emit('join-plot', {
                plotId: this.plotId,
                player: this.currentPlayer
            });
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.isConnected = false;
            this.updateStatus('Disconnected from server');
        });

        this.socket.on('room-members', (members) => {
            console.log('Current room members:', members);
            members.forEach(member => {
                this.addPlayerToGrid(member.player);
            });
        });

        this.socket.on('user-joined', (data) => {
            console.log('User joined:', data);
            this.addPlayerToGrid(data.player);
            this.updateStatus(`${data.player.username} joined the voice chat`);
        });

        this.socket.on('user-left', (data) => {
            console.log('User left:', data);
            this.removePlayerFromGrid(data.socketId);
            this.closePeerConnection(data.socketId);
        });

        // WebRTC signaling
        this.socket.on('offer', async (data) => {
            await this.handleOffer(data);
        });

        this.socket.on('answer', async (data) => {
            await this.handleAnswer(data);
        });

        this.socket.on('ice-candidate', async (data) => {
            await this.handleIceCandidate(data);
        });
    }

    setupEventListeners() {
        document.getElementById('mic-btn').addEventListener('click', () => {
            this.toggleMicrophone();
        });

        document.getElementById('leave-btn').addEventListener('click', () => {
            this.leaveVoiceChat();
        });
    }

    async toggleMicrophone() {
        const micBtn = document.getElementById('mic-btn');
        
        if (!this.isMicEnabled) {
            try {
                this.localStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: true, 
                    video: false 
                });
                
                this.isMicEnabled = true;
                micBtn.textContent = '🔇 Disable Microphone';
                micBtn.classList.remove('btn-primary');
                micBtn.classList.add('btn-danger');
                this.updateStatus('Microphone enabled - you can now talk!');

                // Setup voice activity detection
                this.setupVoiceActivityDetection();

            } catch (error) {
                console.error('Error accessing microphone:', error);
                this.updateStatus('Failed to access microphone. Please check permissions.');
            }
        } else {
            this.disableMicrophone();
        }
    }

    disableMicrophone() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        this.isMicEnabled = false;
        const micBtn = document.getElementById('mic-btn');
        micBtn.textContent = '🎤 Enable Microphone';
        micBtn.classList.remove('btn-danger');
        micBtn.classList.add('btn-primary');
        this.updateStatus('Microphone disabled');

        // Close all peer connections
        this.peerConnections.forEach((pc, socketId) => {
            this.closePeerConnection(socketId);
        });
    }

    setupVoiceActivityDetection() {
        if (!this.localStream) return;

        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(this.localStream);
        const dataArray = new Uint8Array(analyser.frequencyBinCount);

        microphone.connect(analyser);
        analyser.fftSize = 256;

        const checkAudioLevel = () => {
            analyser.getByteFrequencyData(dataArray);
            const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
            
            const playerCard = document.querySelector('.player-card[data-current="true"]');
            if (playerCard) {
                if (average > 20) { // Threshold for voice activity
                    playerCard.classList.add('speaking');
                } else {
                    playerCard.classList.remove('speaking');
                }
            }

            if (this.isMicEnabled) {
                requestAnimationFrame(checkAudioLevel);
            }
        };

        checkAudioLevel();
    }

    addPlayerToGrid(player, isCurrent = false) {
        const playersGrid = document.getElementById('players-grid');
        
        // Check if player already exists
        if (document.querySelector(`[data-player="${player.username}"]`)) {
            return;
        }

        const playerCard = document.createElement('div');
        playerCard.className = 'player-card';
        playerCard.setAttribute('data-player', player.username);
        if (isCurrent) {
            playerCard.setAttribute('data-current', 'true');
        }

        playerCard.innerHTML = `
            <img src="${player.headUrl}" alt="${player.username}" class="player-head">
            <div class="player-name">${player.username}</div>
        `;

        playersGrid.appendChild(playerCard);
    }

    removePlayerFromGrid(socketId) {
        // Note: We'd need to track socketId to player mapping for this to work properly
        // For now, this is a placeholder
        console.log('Removing player with socket ID:', socketId);
    }

    async createPeerConnection(targetSocketId) {
        const peerConnection = new RTCPeerConnection({
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        });

        // Add local stream
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, this.localStream);
            });
        }

        // Handle remote stream
        peerConnection.ontrack = (event) => {
            const remoteAudio = new Audio();
            remoteAudio.srcObject = event.streams[0];
            remoteAudio.play();
        };

        // Handle ICE candidates
        peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.socket.emit('ice-candidate', {
                    target: targetSocketId,
                    candidate: event.candidate
                });
            }
        };

        this.peerConnections.set(targetSocketId, peerConnection);
        return peerConnection;
    }

    async handleOffer(data) {
        const peerConnection = await this.createPeerConnection(data.sender);
        await peerConnection.setRemoteDescription(data.offer);
        
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);
        
        this.socket.emit('answer', {
            target: data.sender,
            answer: answer
        });
    }

    async handleAnswer(data) {
        const peerConnection = this.peerConnections.get(data.sender);
        if (peerConnection) {
            await peerConnection.setRemoteDescription(data.answer);
        }
    }

    async handleIceCandidate(data) {
        const peerConnection = this.peerConnections.get(data.sender);
        if (peerConnection) {
            await peerConnection.addIceCandidate(data.candidate);
        }
    }

    closePeerConnection(socketId) {
        const peerConnection = this.peerConnections.get(socketId);
        if (peerConnection) {
            peerConnection.close();
            this.peerConnections.delete(socketId);
        }
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
    }

    leaveVoiceChat() {
        this.disableMicrophone();
        if (this.socket) {
            this.socket.disconnect();
        }
        window.location.href = 'about:blank';
    }
}

// Initialize voice chat when page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceChat();
});
