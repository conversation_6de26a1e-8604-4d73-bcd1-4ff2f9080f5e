/**
 * Google Apps Script for McDiamondFire Voice Chat Username Encryption
 * 
 * This script provides a secure API endpoint for encrypting usernames
 * that can be called from the McDiamondFire Minecraft server.
 * 
 * Setup Instructions:
 * 1. Create a new Google Apps Script project
 * 2. Replace the default Code.gs with this content
 * 3. Set up the required script properties (File > Project properties > Script properties):
 *    - VOICE_CHAT_SERVER_URL: Your voice chat server URL (e.g., https://your-app.vercel.app)
 *    - API_SECRET_KEY: Same secret key as in your .env file
 * 4. Deploy as a web app with execute permissions for "Anyone"
 * 5. Use the web app URL in your McDiamondFire server
 */

function doPost(e) {
  try {
    // Parse the request
    const data = JSON.parse(e.postData.contents);
    const { username, apiKey, plotId } = data;
    
    // Validate required fields
    if (!username || !apiKey || !plotId) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Missing required fields: username, apiKey, plotId'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Validate API key
    const expectedApiKey = PropertiesService.getScriptProperties().getProperty('API_SECRET_KEY');
    if (apiKey !== expectedApiKey) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid API key'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Get server URL
    const serverUrl = PropertiesService.getScriptProperties().getProperty('VOICE_CHAT_SERVER_URL');
    if (!serverUrl) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Server URL not configured'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Call the voice chat server to encrypt the username
    const response = UrlFetchApp.fetch(serverUrl + '/api/encrypt-username', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify({
        username: username,
        apiKey: apiKey
      })
    });
    
    const responseData = JSON.parse(response.getContentText());
    
    if (response.getResponseCode() === 200) {
      // Generate the voice chat URL
      const voiceChatUrl = `${serverUrl}?plot=${plotId}&user=${encodeURIComponent(responseData.encryptedUsername)}`;
      
      return ContentService
        .createTextOutput(JSON.stringify({
          success: true,
          encryptedUsername: responseData.encryptedUsername,
          voiceChatUrl: voiceChatUrl
        }))
        .setMimeType(ContentService.MimeType.JSON);
    } else {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: responseData.error || 'Failed to encrypt username'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
  } catch (error) {
    Logger.log('Error in doPost: ' + error.toString());
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Internal server error'
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Simple health check endpoint
  return ContentService
    .createTextOutput(JSON.stringify({
      status: 'OK',
      message: 'McDiamondFire Voice Chat API',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Test function to verify the setup
 * Run this function in the Apps Script editor to test your configuration
 */
function testConfiguration() {
  const serverUrl = PropertiesService.getScriptProperties().getProperty('VOICE_CHAT_SERVER_URL');
  const apiKey = PropertiesService.getScriptProperties().getProperty('API_SECRET_KEY');
  
  Logger.log('Server URL: ' + serverUrl);
  Logger.log('API Key configured: ' + (apiKey ? 'Yes' : 'No'));
  
  if (!serverUrl || !apiKey) {
    Logger.log('ERROR: Missing required script properties. Please configure VOICE_CHAT_SERVER_URL and API_SECRET_KEY');
    return false;
  }
  
  // Test the encryption endpoint
  try {
    const testData = {
      username: 'TestPlayer',
      apiKey: apiKey
    };
    
    const response = UrlFetchApp.fetch(serverUrl + '/api/encrypt-username', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(testData)
    });
    
    Logger.log('Test response code: ' + response.getResponseCode());
    Logger.log('Test response: ' + response.getContentText());
    
    return response.getResponseCode() === 200;
  } catch (error) {
    Logger.log('Test failed: ' + error.toString());
    return false;
  }
}
