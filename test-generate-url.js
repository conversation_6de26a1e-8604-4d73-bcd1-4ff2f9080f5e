const axios = require('axios');

async function generateVoiceUrl() {
  try {
    const response = await axios.post('http://localhost:5000/api/generate-voice-url', {
      username: "HoldenHotMoves",
      plotId: "1846"
    });
    
    console.log('Success:', response.data);
    console.log('\n🎮 Voice Chat URL:');
    console.log(response.data.voiceChatUrl);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

generateVoiceUrl();
