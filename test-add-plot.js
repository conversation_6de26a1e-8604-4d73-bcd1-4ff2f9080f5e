const axios = require('axios');

async function addPlot() {
  try {
    const response = await axios.post('http://localhost:5000/api/admin/add-plot', {
      plotId: "1846",
      name: "test",
      owner: "HoldenHotMoves",
      admin<PERSON>ey: "tomosora"
    });
    
    console.log('Success:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

addPlot();
